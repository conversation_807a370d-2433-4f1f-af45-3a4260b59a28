import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { getReferres, getSalesPersonAndPM } from '../../logic/apis/company'
import TabBar from '../../shared/components/tabBar/TabBar'
import ActionButtons from '../../shared/components/actionButtons'
import { CustomModal } from '../../shared/customModal/CustomModal'
import FilterSvg from '../../assets/newIcons/filter.svg'
import {
  convertFilters,
  dayjsFormat,
  exportToCSV,
  formatAddress,
  formatPhoneNumber,
  isSuccess,
  notify,
} from '../../shared/helpers/util'
import * as SharedStyled from '../../styles/styled'
import ReferrerModal from '../Refferer/components/referrerModal/ReferrerModal'
import { SettingsCont } from '../units/style'
import { AddCityModal } from './components/addCityModal/AddCityModal'
import { AddNewContactModal } from './components/addNewContactModal/AddNewContactModal'
import DeletedContact from './components/deletedContact/DeletedContact'
import * as Styled from './style'
import FiltersPanel from '../../shared/advancedFilter/FiltersPanel'
import { DropdownContainer } from '../../shared/dropdownWithCheckboxes/style'
import { ContactFields, Types, TypesDropdown } from './constant'
import { PaginatedTable } from '../../shared/table/PaginatedTable'
import { useNavigate } from 'react-router-dom'
import useDebounce from '../../shared/hooks/useDebounce'
import { ProfileCont } from '../../shared/components/profileInfo/style'
import { AvatarSvg } from '../../shared/helpers/images'
import { deleteMultipleContacts, getContacts, getImportedContacts } from '../../logic/apis/contact'
import { convertFiltersToMongoQuery } from '../../shared/advancedFilter/constant'
import { I_SalesPerson } from '../sales/AddOpportunityModal'
import { useDispatch, useSelector } from 'react-redux'
import { RootState } from '../../logic/redux/store'
import { setContactsAdvancedFilters, setContactsSearchValue } from '../../logic/redux/actions/ui'

interface I_Data {
  clientName: string
  status: string
  address: string
  phone: string
  email: string
}

const Contact = () => {
  const [loading, setLoading] = useState<boolean>(false)
  const [loadingExport, setLoadingExport] = useState<boolean>(false)
  const [showAddCityModal, setShowAddCityModal] = useState<boolean>(false)
  const [addNewClientModal, setShowAddNewClientModal] = useState(false)
  const [refererres, setRefererres] = useState<any>([])
  const [referrerModal, setShowReferrerModal] = useState(false)
  const [referrerValue, setReferrerValue] = useState<any>([])
  const [activeTab, setActiveTab] = useState(0)
  const [showFilter, setShowFilter] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const [totalItems, setTotalItems] = useState(0)
  const [totalPages, setTotalPages] = useState(0)

  const [pageIndex, setPageIndex] = useState(0)
  const [pageSize, setpageSize] = useState(0)
  // State for search and filters
  // const [searchValue, setSearchValue] = useState('')
  // const [advancedFilters, setAdvancedFilters] = useState<Record<string, any[]>>({})
  const [detailsUpdate, setDetailsUpdate] = useState(false)
  const [data, setData] = useState<I_Data[]>([])
  const [dataImport, setDataImport] = useState<I_Data[]>([])
  const fetchIdRef = useRef(0)
  const [salesPersonDrop, setSalesPersonDrop] = useState<I_SalesPerson[]>([])
  const [selectedContactIds, setSelectedContactIds] = useState<string[]>([])
  const loadMoreRef = useRef(null)
  // const [referrerDropdownData, setReferrerDropdownData] = useState<any>([])
  const navigate = useNavigate()

  const dispatch = useDispatch()
  const { contactsSearchValue, contactsAdvancedFilters } = useSelector((state: RootState) => state.ui)
  const debouncedValue = useDebounce(contactsSearchValue, 500)

  // Use these functions instead of the setState functions
  const setSearchValue = (value: string) => {
    dispatch(setContactsSearchValue(value))
  }

  const setAdvancedFilters = (filters: Record<string, any[]>) => {
    dispatch(setContactsAdvancedFilters(filters))
  }

  // Handle click outside to close the filter dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowFilter(false)
      }
    }

    if (showFilter) {
      document.addEventListener('mousedown', handleClickOutside)
    } else {
      document.removeEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showFilter])

  useEffect(() => {
    initFetchReferrers()
  }, [])

  // useEffect(() => {
  //   const filterWithNameAndSymbol = refererres?.map((item: any) => item.name)
  //   // filterWithNameAndSymbol.push('--Add New--')
  //   setReferrerDropdownData(filterWithNameAndSymbol)
  // }, [refererres])

  const fetchImportedContacts = async ({ pageSize, pageIndex, search, contactsAdvancedFilters }: any) => {
    try {
      const queryParams: any = {
        deleted: activeTab === 0 ? false : true,
        limit: pageSize || 10,
        skip: pageIndex + 1,
        search,
        filter: convertFilters(contactsAdvancedFilters || []),
      }

      const res = await getImportedContacts(queryParams)
      if (isSuccess(res)) {
        const { contacts, pagination } = res?.data?.data
        setDataImport(contacts)
        setTotalItems(pagination.totalItems)
        setTotalPages(pagination.totalPages)
      }
    } catch (error) {
      console.log(error)
    }
  }

  const initFetchReferrers = async () => {
    try {
      const res = await getReferres(false, true)
      if (isSuccess(res)) {
        const { referrers } = res?.data?.data
        setRefererres(referrers)
      }
    } catch (error) {
      console.log(error)
    }
  }
  useEffect(() => {
    if (referrerValue === '--Add New--') {
      setShowReferrerModal(true)
    }
  }, [referrerValue])

  const fetchData = useCallback(
    async ({ pageSize, pageIndex, search, contactsAdvancedFilters }: any) => {
      try {
        // This will get called when the table needs new data
        setData([])
        setLoading(true)
        let receivedData: any = []

        // Build query parameters from advanced filters
        const queryParams: any = {
          deleted: activeTab === 0 ? false : true,
          limit: pageSize || 10,
          skip: pageIndex + 1,
          search,
          filter: convertFilters(contactsAdvancedFilters || []),
        }

        // // Add any additional filters from the advanced filter panel
        // if (Object.keys(contactsAdvancedFilters).length > 0) {
        //   const mongoQuery = convertFiltersToMongoQuery(contactsAdvancedFilters)
        //   console.log('Advanced filters query:', mongoQuery)

        //   // Add filters to query params
        //   // This is just an example - you'll need to adapt this to your API's requirements
        //   if (Object.entries(mongoQuery).length) {
        //     queryParams.status = mongoQuery
        //   }
        // }

        const contactResponse = await getContacts(queryParams)

        if (isSuccess(contactResponse)) {
          let statusRes = contactResponse?.data?.data?.data
          statusRes.forEach((res: any) => {
            receivedData.push({
              fullName: res?.fullName,
              firstName: res?.firstName,
              lastName: res?.lastName,
              dateReceived: res?.dateReceived,
              tags: res?.tags,
              businessName: res?.businessName,
              street: res?.street,
              city: res?.city,
              state: res?.state,
              zip: res?.zip,
              phone: formatPhoneNumber(res?.phone, '') || '--',
              email: res?.email,
              contactId: res._id,
              leadSource: res?.leadSourceName || '--',
              isBusiness: res?.isBusiness || false,
              isDeleted: false,
              name: res?.name,
              type: Object.entries(Types).find(([_, v]) => v === res?.type)?.[0] || '--',
            })
          })
          const pagination = contactResponse?.data?.data?.pagination || {
            totalItems: 0,
            totalPages: 0,
          }
          console.log({ receivedData })
          setData(receivedData)
          setTotalItems(pagination.totalItems)
          setTotalPages(pagination.totalPages)
        } else {
          notify(contactResponse?.data?.message, 'error')
        }

        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current
      } catch (error) {
        console.error('TeamTable fetchData error', error)
      } finally {
        setLoading(false)
      }
    },
    [detailsUpdate, activeTab]
  )

  const getPositionMembers = async () => {
    try {
      const response = await getSalesPersonAndPM()
      if (isSuccess(response)) {
        setSalesPersonDrop(response?.data?.data?.members)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    }
  }
  useEffect(() => {
    getPositionMembers()
  }, [])

  // Fetch data when activeTab, contactsAdvancedFilters, or contactsSearchValue change
  useEffect(() => {
    if (activeTab === 2)
      fetchImportedContacts({
        pageSize: pageSize,
        pageIndex: pageIndex,
        search: debouncedValue,
        contactsAdvancedFilters,
      })

    if (activeTab === 0)
      fetchData({ pageSize: pageSize, pageIndex: pageIndex, search: debouncedValue, contactsAdvancedFilters })
  }, [contactsAdvancedFilters, debouncedValue, activeTab, pageIndex, pageSize])

  const columns: any = useMemo(
    () => [
      {
        Header: 'Name',
        accessor: 'name', // accessor is the "key" in the data
        Cell: (props: any) => {
          return (
            <ProfileCont>
              <img src={AvatarSvg} alt="hello" />
              <SharedStyled.FlexCol gap="2px">
                <h1>
                  {props?.row?.original?.isBusiness
                    ? props?.row?.original?.fullName || props?.row?.original?.businessName
                    : props?.row?.original?.fullName}
                </h1>
                <p>{props?.row?.original?.email}</p>
              </SharedStyled.FlexCol>
            </ProfileCont>
          )
        },
      },
      {
        Header: 'Address',
        accessor: 'address',
        Cell: (props: any) => {
          return (
            <Styled.AddressWrap>
              {props?.row?.original?.street ? <p>{props?.row?.original?.street},</p> : null}

              <p>
                {props?.row?.original?.city ? `${props?.row?.original?.city},` : null} {props?.row?.original?.state}{' '}
                {props?.row?.original?.zip}
              </p>
            </Styled.AddressWrap>
          )
        },
      },
      {
        Header: 'Phone',
        accessor: 'phone',
      },
      {
        Header: 'Lead Source',
        accessor: 'leadSource',
      },
      {
        Header: 'Type',
        accessor: 'type',
      },
    ],
    []
  )

  const columnsImported: any = useMemo(
    () => [
      {
        Header: 'Date Imported',
        Cell: (props: any) => {
          return <>{dayjsFormat(props?.row?.original?.dateReceived, 'M/D/YY') || '--'}</>
        },
      },
      {
        Header: 'Name',
        accessor: 'name', // accessor is the "key" in the data
        Cell: (props: any) => {
          return (
            <ProfileCont>
              <img src={AvatarSvg} alt="hello" />
              <SharedStyled.FlexCol gap="2px">
                <h1>
                  {props?.row?.original?.isBusiness
                    ? props?.row?.original?.fullName || props?.row?.original?.businessName
                    : props?.row?.original?.fullName}
                </h1>
                <p>{props?.row?.original?.email}</p>
              </SharedStyled.FlexCol>
            </ProfileCont>
          )
        },
      },
      {
        Header: 'Phone',
        Cell: (props: any) => {
          return <>{formatPhoneNumber(props?.row?.original?.phone, '') || '--'}</>
        },
      },
      {
        Header: 'Email',
        accessor: 'email',
      },
    ],
    []
  )

  const fields: {
    label: string
    key: string
    type: 'string' | 'dropdown' | 'date' | 'number'
    values?: any
    operators?: string[]
  }[] = [
    { label: 'Address', key: 'fullAddress', type: 'string' },
    {
      label: 'Assigned To',
      key: 'salesPerson',
      type: 'dropdown',
      values: salesPersonDrop,
      operators: ['is', 'is_not'],
    },
    { label: 'Birth Date', key: 'dateOfBirth', type: 'date' },
    { label: 'Business Name', key: 'businessName', type: 'string' },
    { label: 'City', key: 'city', type: 'string' },
    { label: 'Created', key: 'createdAt', type: 'date' },
    { label: 'Referrals', key: 'referrer', type: 'number' },
    { label: 'State', key: 'state', type: 'string' },
    { label: 'Status', key: 'type', type: 'dropdown', values: TypesDropdown, operators: ['is', 'is_not'] },
    {
      label: 'Duplicates',
      key: 'duplicates',
      type: 'dropdown',
      values: ContactFields,
      operators: ['has_duplicates'],
    },
  ]

  const handleDeleteMultipleContacts = async () => {
    if (selectedContactIds.length === 0) {
      notify('Please select at least one contact', 'warning')
      return
    }

    // Confirm deletion
    if (window.confirm(`Are you sure you want to delete ${selectedContactIds.length} contact(s)?`)) {
      try {
        // Call the API to delete multiple contacts
        const response = await deleteMultipleContacts(selectedContactIds)

        if (isSuccess(response)) {
          notify(`Successfully deleted ${selectedContactIds.length} contact(s)`, 'success')

          // Clear selected contacts
          setSelectedContactIds([])

          // Refresh the data
          fetchData({
            pageSize,
            pageIndex,
            search: debouncedValue,
            contactsAdvancedFilters,
          })
        } else {
          notify(response?.data?.message || 'Failed to delete contacts', 'error')
        }
      } catch (error) {
        console.error('Delete multiple contacts error:', error)
        notify('An error occurred while deleting contacts', 'error')
      } finally {
      }
    }
  }

  const handleExportSelectedContacts = async () => {
    try {
      setLoadingExport(true)

      let contactsToExport

      if (selectedContactIds.length === 0) {
        // Export all contacts with current filters
        const queryParams = {
          deleted: activeTab === 0 ? false : true,
          limit: totalItems,
          skip: 1,
          search: debouncedValue,
          filter: convertFilters(contactsAdvancedFilters || []),
        }

        const response = await getContacts(queryParams)

        if (!isSuccess(response)) {
          notify(response?.data?.message || 'Failed to fetch contacts', 'error')
          return
        }

        contactsToExport = response?.data?.data?.data || []
      } else {
        // Use already loaded data for selected contacts
        contactsToExport = data.filter(({ contactId }) => selectedContactIds.includes(contactId))
      }

      if (!contactsToExport || contactsToExport.length === 0) {
        notify('No contacts available to export', 'warning')
        return
      }

      // Format data for CSV export - use map with destructuring for clarity
      const csvData = contactsToExport.map(
        ({
          isBusiness,
          businessName,
          fullName,
          firstName,
          lastName,
          dateReceived,
          email,
          phone,
          street,
          city,
          state,
          zip,
          leadSource,
          type,
          tags,
        }: any) => ({
          Name: isBusiness ? businessName : fullName,
          'First Name': firstName || '',
          'Last Name': lastName || '',
          Business: isBusiness || false,
          'Date Received': dateReceived || '',
          Email: email || '',
          Phone: phone || '',
          Address: `${street || ''} ${city || ''} ${state || ''} ${zip || ''}`.trim(),
          'Lead Source': leadSource || '',
          Type: type || '',
          Tags: Array.isArray(tags) ? tags.join(',') : tags || '',
        })
      )

      // Generate filename with current date
      const filename = `contacts_export_${new Date().toISOString().split('T')[0]}.csv`

      // Export to CSV
      exportToCSV(csvData, filename)

      notify(`Successfully exported ${contactsToExport.length} contact(s)`, 'success')
    } catch (error) {
      console.error('Export error:', error)
      notify('An error occurred while exporting contacts', 'error')
    } finally {
      setLoadingExport(false)
    }
  }

  return (
    <SettingsCont gap="24px">
      <SharedStyled.SectionTitle>Contacts</SharedStyled.SectionTitle>

      <SharedStyled.FlexRow alignItems="flex-start">
        <SharedStyled.FlexCol gap="10px" width="100%">
          <TabBar
            onTabChange={setActiveTab}
            tabs={[
              {
                title: 'Active',
                render: () => (
                  <>
                    {/* Action buttons row - just below tabs and above table */}
                    <SharedStyled.FlexRow justifyContent="space-between" gap="12px" margin="16px 0" padding="0 16px">
                      <ActionButtons
                        onAddClick={() => setShowAddNewClientModal(true)}
                        onMessageClick={() => console.log('Message clicked')}
                        onEmailClick={() => console.log('Email clicked')}
                        onAddTagsClick={() => console.log('Add Tags clicked')}
                        onRemoveTagsClick={() => console.log('Remove Tags clicked')}
                        onDeleteClick={() => handleDeleteMultipleContacts()}
                        onExportClick={() => handleExportSelectedContacts()}
                        loadingExport={loadingExport}
                      />
                      <SharedStyled.TooltipContainer
                        width="250px"
                        positionLeft="4px"
                        positionBottom="0px"
                        positionLeftDecs="0px"
                        positionBottomDecs="40px"
                      >
                        <span className="tooltip-content">
                          Search By Name, Email (Primary + Addition), Business Name, Tags or Phone (Primary + Addition).
                          Customize here
                        </span>
                        <div>
                          <Styled.SearchInput
                            type="search"
                            placeholder="Search..."
                            value={contactsSearchValue}
                            onChange={(e) => setSearchValue(e.target.value)}
                          />
                        </div>
                      </SharedStyled.TooltipContainer>
                    </SharedStyled.FlexRow>

                    <PaginatedTable
                      columns={columns}
                      data={data || []}
                      loading={loading}
                      totalItems={totalItems}
                      pageCount={totalPages}
                      // fetchData={fetchData}
                      fetchData={(params) => {
                        setPageIndex(params.pageIndex)
                        setpageSize(params.pageSize)
                      }}
                      // setPageIndex={setPageIndex}
                      // setpageSize={setpageSize}
                      onRowClick={(data) => {
                        navigate(`/contact/profile/${data.contactId}/${data.isDeleted}`)
                      }}
                      isLoadMoreLoading={loading}
                      pageKey="contacts-active"
                      selectable={true}
                      selectedIds={selectedContactIds}
                      setSelectedIds={setSelectedContactIds}
                      idField="contactId"
                    />
                  </>
                ),
              },
              {
                title: 'Inactive',
                render: () => (
                  <>
                    <PaginatedTable
                      columns={columns}
                      data={data || []}
                      loading={loading}
                      totalItems={totalItems}
                      pageCount={totalPages}
                      fetchData={(params) => {
                        setPageIndex(params.pageIndex)
                        setpageSize(params.pageSize)
                      }}
                      // setPageIndex={setPageIndex}
                      // setpageSize={setpageSize}
                      onRowClick={(data) => {
                        navigate(`/contact/profile/${data.contactId}/true`)
                      }}
                      isLoadMoreLoading={loading}
                      pageKey="contacts-inactive"
                    />
                  </>
                ),
                // render: () => <DeletedContact />,
              },
              {
                title: 'Imported',
                render: () => (
                  <>
                    <PaginatedTable
                      columns={columnsImported}
                      data={dataImport || []}
                      loading={loading}
                      totalItems={totalItems}
                      pageCount={totalPages}
                      fetchData={(params) => {
                        setPageIndex(params.pageIndex)
                        setpageSize(params.pageSize)
                      }}
                      // setPageIndex={setPageIndex}
                      // setpageSize={setpageSize}
                      onRowClick={(data) => {
                        navigate(`/contact/profile/${data.contactId}/true`)
                      }}
                      isLoadMoreLoading={loading}
                      pageKey="contacts-inactive"
                    />
                  </>
                ),
                // render: () => <DeletedContact />,
              },
            ]}
            filterComponent={
              activeTab === 0 ? (
                <SharedStyled.FlexRow width="auto" gap="16px" alignItems="center">
                  {/* Search Input */}

                  {/* Filter Icon */}
                  <DropdownContainer ref={dropdownRef} marginTop={'0px'}>
                    <Styled.FilterButtonWrapper onClick={() => setShowFilter((prev) => !prev)}>
                      Filter
                      <img
                        src={FilterSvg}
                        className="filter-icon"
                        alt="filter icon"
                        style={{ width: '20px', cursor: 'pointer' }}
                      />
                      {/* Filter count badge */}
                      {Object.keys(contactsAdvancedFilters).length > 0 && (
                        <div
                          style={{
                            position: 'absolute',
                            top: '-8px',
                            right: '-8px',
                            backgroundColor: '#3b82f6',
                            color: 'white',
                            borderRadius: '50%',
                            width: '20px',
                            height: '20px',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            fontSize: '12px',
                            fontWeight: 'bold',
                          }}
                        >
                          {Object.keys(contactsAdvancedFilters).length}
                        </div>
                      )}
                    </Styled.FilterButtonWrapper>

                    {showFilter && (
                      <div
                        style={{
                          position: 'absolute',
                          right: 0,
                          top: '40px',
                          zIndex: 100,
                          width: '350px',
                          backgroundColor: '#fff',
                          boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
                          borderRadius: '8px',
                          overflow: 'hidden',
                          maxHeight: '80vh',
                          overflowY: 'auto',
                        }}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <div style={{ padding: '16px' }}>
                          <h3 style={{ marginTop: 0, marginBottom: '16px' }}>Filter by</h3>
                          <FiltersPanel
                            fields={fields}
                            width="100%"
                            background="#ffffff"
                            initialFilters={contactsAdvancedFilters}
                            onResetAll={() => setShowFilter(false)}
                            onFiltersApplied={(query) => {
                              setAdvancedFilters(query)
                              // This will trigger a re-fetch of data with the new filters
                            }}
                          />
                        </div>
                      </div>
                    )}
                  </DropdownContainer>
                </SharedStyled.FlexRow>
              ) : null
            }
          />
        </SharedStyled.FlexCol>
      </SharedStyled.FlexRow>

      <CustomModal show={addNewClientModal}>
        <AddNewContactModal
          setShowAddNewClientModal={setShowAddNewClientModal}
          setDetailsUpdate={setDetailsUpdate}
          detailsUpdate={detailsUpdate}
          onComplete={() => {
            fetchData({ pageSize: pageSize, pageIndex: pageIndex, search: debouncedValue, contactsAdvancedFilters })
          }}
          // setShowAddCityModal={setShowAddCityModal}
          onClose={() => {
            setShowAddNewClientModal(false)
          }}
          // referrerDropdownData={referrerDropdownData}
          refererres={refererres}
          setReferrerValue={setReferrerValue}
          setShowReferrerModal={setShowReferrerModal}
        />
      </CustomModal>
      <CustomModal show={referrerModal}>
        <ReferrerModal
          onClose={() => {
            setShowReferrerModal(false)
          }}
          onComplete={() => {
            initFetchReferrers()
          }}
        />
      </CustomModal>
      <CustomModal show={showAddCityModal}>
        <AddCityModal setShowAddCityModal={setShowAddCityModal} action="Add City" setDetailsUpdate={setDetailsUpdate} />
      </CustomModal>
    </SettingsCont>
  )
}

export default Contact
