import { Link, useLocation } from 'react-router-dom'
import { Text } from '../../../../../styles/styled'
import { commentColorType } from '../../../constant'

const Pill = ({ numVal, text, margin, path }: { numVal?: string; text?: string; margin?: string; path: string }) => {
  return (
    <Link
      to={path}
      target="_blank"
      style={{
        pointerEvents: !path ? 'none' : 'auto',
      }}
    >
      <Text
        padding="2px 8px"
        style={{
          textTransform: 'uppercase',
        }}
        backgroundColor={
          numVal
            ? Number(commentColorType[numVal]) > 10
              ? commentColorType['10']
              : commentColorType[numVal]
            : '#bcbcbc'
        }
        color="white"
        fontSize="12px"
        margin={margin}
        fontWeight="bold"
        borderRadius="5px"
      >
        {text}
      </Text>
    </Link>
  )
}

export default Pill
